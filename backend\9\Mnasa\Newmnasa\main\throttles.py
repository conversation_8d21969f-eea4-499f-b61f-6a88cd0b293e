"""
Custom throttle classes for the quiz system
"""
from rest_framework.throttling import ScopedRateThrottle


class QuizSaveAnswerThrottle(ScopedRateThrottle):
    """
    Throttle class specifically for quiz save_answer actions
    Uses a higher rate limit to allow frequent answer saving
    """
    scope = 'quiz_save'

    def allow_request(self, request, view):
        # Only apply this throttle to save_answer action
        if hasattr(view, 'action') and view.action == 'save_answer':
            return super().allow_request(request, view)
        # For other actions, don't throttle (return True)
        return True


class QuizGeneralThrottle(ScopedRateThrottle):
    """
    General throttle class for quiz-related actions
    """
    scope = 'quiz'

    def allow_request(self, request, view):
        # Don't apply to save_answer (handled by QuizSaveAnswerThrottle)
        if hasattr(view, 'action') and view.action == 'save_answer':
            return True
        return super().allow_request(request, view)
