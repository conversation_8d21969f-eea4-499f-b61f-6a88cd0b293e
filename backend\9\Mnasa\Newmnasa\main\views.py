from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from django.conf import settings
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model, authenticate

# Hossam third code of hls
from .video_converter import VideoConverter

# Custom throttles for quiz system
from .throttles import QuizSaveAnswerThrottle, QuizGeneralThrottle

from .models import (
    Course,
    DigitalProduct,
    Order,
    Payment,
    Lesson,
    Enrollment,
    Quiz,
    Question,
    Answer,
    UserQuizAttempt,
    Certificate,
    Announcement,
    FAQ,
    Category,
    InstructorProfile,
    Review,
    InstructorAvailability,
    Notification,
    ReviewComment,
)
from .serializers import (
    UserSerializer,
    CourseReadSerializer,
    CourseWriteSerializer,
    ReviewSerializer,
    InstructorWithCoursesSerializer,
    DigitalProductSerializer,
    OrderSerializer,
    PaymentSerializer,
    LessonSerializer,
    QuizSerializer,
    QuestionSerializer,
    CertificateSerializer,
    AnnouncementSerializer,
    FAQSerializer,
    CategorySerializer,
    InstructorProfileSerializer,
    InstructorAvailabilitySerializer,
    NotificationSerializer,
    AnswerSerializer,
)
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
import uuid
import hmac
import hashlib
import time
from datetime import datetime, timedelta
import os
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from PIL import Image, ImageDraw, ImageFont
import io
import boto3
from botocore.exceptions import ClientError
import ffmpeg
import tempfile
from rest_framework.views import APIView
import jwt
import logging
from rest_framework_simplejwt.tokens import RefreshToken
import re
from django.db.models import Q, Sum
from .cloudinary_service import CloudinaryVideoService
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from main.utils.notifications import notify
from main.constants.notification_messages import (
    COURSE_CREATED,
    COURSE_DELETED_STUDENT,
    COURSE_DELETED_INSTRUCTOR,
    COURSE_UPDATED,
    VIDEO_UPLOADED,
    VIDEO_DELETED,
    ANNOUNCEMENT_NEW,
    PAYMENT_RECEIVED,
    PAYMENT_FOR_COURSE,
    CERTIFICATE_ISSUED,
    QUIZ_CERTIFICATE_CONGRATS,
    REVIEW_COMMENT_REPLY,
    REVIEW_COMMENT_NEW,
    REVIEW_COMMENT_EDIT,
    REVIEW_REPLY_EDIT,
    REVIEW_COMMENT_DELETE,
    REVIEW_EDIT,
    REVIEW_DELETE,
    REVIEW_APPROVED,
    ACCOUNT_UPDATED,
    PASSWORD_CHANGED,
)
from main.constants.notification_types import (
    COURSE,
    REVIEW,
    PAYMENT,
    CERTIFICATE,
    SYSTEM,
)
from main.tasks import send_bulk_notification
import traceback
from .permissions import IsInstructor

logger = logging.getLogger(__name__)
User = get_user_model()

class LessonResourceUploadView(APIView):
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request, pk):
        lesson = Lesson.objects.get(pk=pk)
        file = request.FILES.get("resources")
        if not file:
            return Response({"error": "No file provided."}, status=400)

        lesson.resources = file
        lesson.save()
        return Response({"resources": lesson.resources.url}, status=status.HTTP_200_OK)
# Utility function لتجميع فلترة الكورسات
def filter_courses_for_user(user):
    if user.is_staff:
        return Course.objects.all()
    if user.is_instructor:
        return Course.objects.filter(instructor=user)
    return Course.objects.filter(Q(is_published=True) | Q(students=user))


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return User.objects.all()
        return User.objects.filter(id=self.request.user.id)

    @action(detail=False, methods=["get"], url_path="list-all")
    def list_all(self, request):
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def all_users(self, request):
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(
        detail=False,
        methods=["post"],
        url_path="change-password",
        permission_classes=[IsAuthenticated],
    )
    def change_password(self, request):
        user = request.user
        current_password = request.data.get("current_password")
        new_password = request.data.get("new_password")
        if not current_password or not new_password:
            return Response(
                {"error": "يجب إدخال كلمة المرور الحالية والجديدة"}, status=400
            )
        if not user.check_password(current_password):
            return Response({"error": "كلمة المرور الحالية غير صحيحة"}, status=400)
        user.set_password(new_password)
        user.save()
        notify(user, PASSWORD_CHANGED, type=SYSTEM)
        return Response({"message": "تم تغيير كلمة المرور بنجاح"})

    def perform_update(self, serializer):
        user = serializer.save()
        # حماية wallet_number من التغيير بعد تعيينه
        if user.wallet_number and User.objects.filter(pk=user.pk).exists():
            original = User.objects.get(pk=user.pk)
            if original.wallet_number and original.wallet_number != user.wallet_number:
                raise serializers.ValidationError(
                    "Wallet number cannot be changed once set."
                )
        # إشعارات عند تغيير الحقول الحساسة
        changed_fields = []
        if hasattr(self, "request") and self.request.method in ["PUT", "PATCH"]:
            original = User.objects.get(pk=user.pk)
            for field, label in [
                ("email", "البريد الإلكتروني"),
                ("phone_number", "رقم الجوال"),
                ("username", "اسم المستخدم"),
            ]:
                if (
                    field in self.request.data
                    and getattr(original, field) != self.request.data[field]
                ):
                    changed_fields.append(label)
        for field_label in changed_fields:
            notify(user, ACCOUNT_UPDATED(field_label), type=SYSTEM)
        return user


class CourseViewSet(viewsets.ModelViewSet):
    queryset = Course.objects.all()
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser, JSONParser)

    def get_permissions(self):
        # السماح فقط للمدرس بالكتابة، لكن القراءة لأي مستخدم مسجل
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            return [IsInstructor()]
        return [IsAuthenticated()]

    def get_serializer_class(self):
        if self.action in ['list', 'retrieve']:
            return CourseReadSerializer
        return CourseWriteSerializer

    def get_queryset(self):
        return filter_courses_for_user(self.request.user)

    def perform_create(self, serializer):
        course = serializer.save(instructor=self.request.user)
        notify(self.request.user, COURSE_CREATED(course.title), type=COURSE)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_course",
        permission_classes=[IsAuthenticated],
    )
    def delete_course(self, request, pk=None):
        course = self.get_object()
        if course.instructor != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)
        title = course.title
        students = course.students.all()
        course.delete()
        # إشعار جماعي للطلاب عبر Celery
        send_bulk_notification.delay(
            [student.id for student in students],
            COURSE_DELETED_STUDENT(title),
            type=COURSE,
        )
        notify(request.user, COURSE_DELETED_INSTRUCTOR(title), type=COURSE)
        return Response({"message": "تم الحذف"}, status=204)

    def perform_update(self, serializer):
        course = serializer.save()
        notify(course.instructor, COURSE_UPDATED(course.title), type=COURSE)
        return course

    @action(detail=True, methods=["get"])
    def lessons(self, request, pk=None):
        course = self.get_object()
        lessons = course.lessons.all()
        serializer = LessonSerializer(lessons, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"], permission_classes=[IsAuthenticated])
    def toggle_like(self, request, pk=None):
        course = self.get_object()
        user = request.user
        if course.likes.filter(id=user.id).exists():
            course.likes.remove(user)
            liked = False
        else:
            course.likes.add(user)
            liked = True
        return Response({"liked": liked, "likes_count": course.likes.count()})

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        print("📂 request.FILES:", request.FILES)

        return Response(serializer.data, status=status.HTTP_200_OK)


    # Hossam Delete Lesson Code
    @action(
        detail=True,
        methods=["delete"],
        url_path="lessons/(?P<lesson_id>[^/.]+)",
        permission_classes=[IsAuthenticated],
    )
    def delete_lesson(self, request, pk=None, lesson_id=None):
        course = self.get_object()
        try:
            lesson = course.lessons.get(id=lesson_id)
        except Lesson.DoesNotExist:
            return Response({"error": "الدرس غير موجود"}, status=404)

        if course.instructor != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)

        lesson.delete()
        return Response({"message": "تم حذف الدرس"}, status=204)


class DigitalProductViewSet(viewsets.ModelViewSet):
    queryset = DigitalProduct.objects.all()
    serializer_class = DigitalProductSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            return DigitalProduct.objects.filter(is_published=True)
        return DigitalProduct.objects.filter(is_published=True)

    def perform_create(self, serializer):
        serializer.save(seller=self.request.user)


class OrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return Order.objects.all()
        return Order.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        order = serializer.save(user=self.request.user)
        # إرسال إشعار بعد الشراء
        if order.course:
            notify(
                self.request.user,
                f"تم تأكيد طلبك بنجاح للدورة: {order.course.title}",
                type=SYSTEM,
            )
        elif order.product:
            notify(
                self.request.user,
                f"تم تأكيد طلبك للمنتج الرقمي: {order.product.title}",
                type=SYSTEM,
            )


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return Payment.objects.all()
        return Payment.objects.filter(order__user=self.request.user)

    def perform_create(self, serializer):
        payment = serializer.save()
        # إشعار للمستخدم الذي دفع
        notify(payment.order.user, PAYMENT_RECEIVED(payment.order.id), type=PAYMENT)
        # إشعار للمدرب إذا كان الطلب مرتبط بكورس وله مدرب
        if payment.order.course and hasattr(payment.order.course, "instructor"):
            notify(
                payment.order.course.instructor,
                PAYMENT_FOR_COURSE(payment.order.course.title),
                type=PAYMENT,
            )


class VideoProtectionService:
    def __init__(self):
        self.s3_client = None
        self.mediaconvert_client = None
        try:
            if all(
                [
                    settings.AWS_ACCESS_KEY_ID,
                    settings.AWS_SECRET_ACCESS_KEY,
                    settings.AWS_REGION,
                ]
            ):
                self.s3_client = boto3.client(
                    "s3",
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                    region_name=settings.AWS_REGION,
                )
                if settings.AWS_MEDIACONVERT_ENDPOINT:
                    self.mediaconvert_client = boto3.client(
                        "mediaconvert",
                        endpoint_url=settings.AWS_MEDIACONVERT_ENDPOINT,
                        region_name=settings.AWS_REGION,
                    )
        except Exception as e:
            logger.error(f"Failed to initialize AWS clients: {str(e)}")
            # لا نرفع الاستثناء هنا، بل نترك الخدمة تعمل بدون AWS

    def process_video(self, video_file, user_id, watermark_text):
        if not self.s3_client:
            raise Exception(
                "AWS S3 client not initialized. Please check AWS credentials."
            )

        try:
            temp_dir = tempfile.mkdtemp()
            input_path = os.path.join(temp_dir, "input.mp4")
            output_path = os.path.join(temp_dir, "output.mp4")

            with open(input_path, "wb") as f:
                f.write(video_file.read())

            self.add_watermark(input_path, output_path, watermark_text)

            s3_key = f"protected_videos/{user_id}/{uuid.uuid4()}.mp4"
            self.s3_client.upload_file(
                output_path, settings.AWS_STORAGE_BUCKET_NAME, s3_key
            )

            if self.mediaconvert_client:
                job = self.create_media_convert_job(s3_key)
                return job["Job"]["Id"]
            else:
                return s3_key

        except Exception as e:
            logger.error(f"Failed to process video: {str(e)}")
            raise
        finally:
            try:
                if os.path.exists(input_path):
                    os.remove(input_path)
                if os.path.exists(output_path):
                    os.remove(output_path)
                if os.path.exists(temp_dir):
                    os.rmdir(temp_dir)
            except Exception as e:
                logger.error(f"Failed to clean up temp files: {str(e)}")

    def add_watermark(self, input_path, output_path, text):
        try:
            stream = ffmpeg.input(input_path)
            stream = ffmpeg.drawtext(
                stream,
                text=text,
                fontfile=settings.FONT_PATH,
                fontsize=24,
                fontcolor="white",
                x="(w-text_w)-20",
                y="(h-text_h)-20",
                box=1,
                boxcolor="black@0.5",
                boxborderw=5,
            )
            stream = ffmpeg.output(stream, output_path)
            ffmpeg.run(stream, overwrite_output=True)
        except Exception as e:
            logger.error(f"Failed to add watermark: {str(e)}")
            raise

    def create_media_convert_job(self, s3_key):
        try:
            job_settings = {
                "OutputGroups": [
                    {
                        "Name": "HLS Output",
                        "OutputGroupSettings": {
                            "HlsGroupSettings": {
                                "Destination": f"s3://{settings.AWS_STORAGE_BUCKET_NAME}/protected_videos/",
                                "SegmentLength": 10,
                                "Encryption": {
                                    "Mode": "AES_128",
                                    "KeyProviderSettings": {
                                        "StaticKeySettings": {
                                            "KeyFormat": "identity",
                                            "KeyFormatVersions": "1",
                                            "StaticKeyValue": settings.VIDEO_ENCRYPTION_KEY,
                                        }
                                    },
                                },
                            }
                        },
                    }
                ],
                "Inputs": [
                    {"FileInput": f"s3://{settings.AWS_STORAGE_BUCKET_NAME}/{s3_key}"}
                ],
            }

            return self.mediaconvert_client.create_job(
                Role=settings.AWS_MEDIACONVERT_ROLE, Settings=job_settings
            )
        except Exception as e:
            logger.error(f"Failed to create media convert job: {str(e)}")
            raise


from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from .models import Lesson, Enrollment
from .cloudinary_service import CloudinaryVideoService
import logging

logger = logging.getLogger(__name__)


class LessonViewSet(viewsets.ModelViewSet):
    queryset = Lesson.objects.all()
    serializer_class = LessonSerializer
    parser_classes = [MultiPartParser, FormParser]  # ✅ أضف هذا السطر
    permission_classes = [permissions.IsAuthenticated]
    @action(detail=True, methods=["delete"])
    def delete_resource(self, request, pk=None):
        lesson = self.get_object()
        if lesson.resources:
            # استخراج public_id من الرابط
            url = lesson.resources.url
            public_id = self.extract_public_id_from_url(url)
            if public_id:
                try:
                    import cloudinary.uploader
                    cloudinary.uploader.destroy(public_id, resource_type="raw")
                except Exception as e:
                    # سجل الخطأ لكن لا توقف العملية
                    logger.error(f"Cloudinary deletion failed: {str(e)}")
            lesson.resources = None
            lesson.save()
        return Response({"message": "تم حذف الملف"}, status=status.HTTP_204_NO_CONTENT)
    def extract_public_id_from_url(self, url):
        # استخلاص الـ public_id من رابط Cloudinary
        match = re.search(r"v\d+/(.+?)\.\w+$", url)
        if match:
            return match.group(1)
        return None

    def get_queryset(self):
        """
        تحسين استعلام الدروس ليشمل فقط الدروس المسموح بها للمستخدم
        """
        user = self.request.user

        if user.is_authenticated:
            # Get lesson IDs for instructor's courses
            instructor_lessons = Lesson.objects.filter(
                course__instructor=user
            ).values_list("id", flat=True)

            # Get lesson IDs for enrolled courses
            enrolled_lessons = Lesson.objects.filter(
                course__in=user.enrolled_courses.all()
            ).values_list("id", flat=True)

            # Get lesson IDs for preview lessons
            preview_lessons = Lesson.objects.filter(is_preview=True).values_list(
                "id", flat=True
            )

            # Combine allowed lesson IDs and filter the queryset
            allowed_lesson_ids = (
                list(instructor_lessons)
                + list(enrolled_lessons)
                + list(preview_lessons)
            )

            # Ensure unique IDs
            unique_allowed_lesson_ids = list(set(allowed_lesson_ids))

            return Lesson.objects.filter(id__in=unique_allowed_lesson_ids)

        return Lesson.objects.none()

    @action(detail=True, methods=["post"])
    def upload_video(self, request, pk=None):
        """
        رفع فيديو جديد إلى Cloudinary وحفظ الرابط في قاعدة البيانات
        """
        lesson = self.get_object()

        # التحقق من أن المستخدم هو المدرب المسؤول عن الدورة
        if lesson.course.instructor != request.user:
            return Response(
                {"error": "You are not authorized to upload videos for this course"},
                status=status.HTTP_403_FORBIDDEN,
            )

        if not request.FILES.get("video"):
            return Response(
                {"error": "No video file provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        try:
            video_file = request.FILES["video"]

            # رفع الفيديو إلى Cloudinary
            result = CloudinaryVideoService.upload_video_as_hls(
                video_file, folder="course_123/lessons"
            )

            # حفظ بيانات الفيديو في قاعدة البيانات
            lesson.video = result["secure_url"]
            lesson.video_public_id = result["public_id"]
            lesson.video_duration = result.get("duration", 0)
            lesson.save()
            notify(request.user, VIDEO_UPLOADED(lesson.title), type=COURSE)
            return Response(
                {
                    "message": "Video uploaded successfully",
                    "data": {
                        "url": result["secure_url"],
                        "duration": result.get("duration"),
                        "format": result.get("format"),
                        "public_id": result["public_id"],
                    },
                },
                status=status.HTTP_201_CREATED,
            )

        except Exception as e:
            logger.error(f"Video upload failed: {str(e)}")
            return Response(
                {"error": "Failed to upload video", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["get"])
    def video_url(self, request, pk=None):
        """
        الحصول على رابط مؤقت وآمن للفيديو من Cloudinary
        """
        lesson = self.get_object()

        # التحقق من وجود الفيديو
        if not lesson.video_public_id:
            return Response(
                {"error": "Video not found"}, status=status.HTTP_404_NOT_FOUND
            )

        # السماح لصاحب الكورس بالوصول مباشرة والتحقق من صلاحية الطلاب
        if not lesson.course.instructor == request.user:
            if not lesson.is_preview:
                # التحقق من الاشتراك عبر students array
                if not lesson.course.students.filter(id=request.user.id).exists():
                    return Response(
                        {"error": "You must enroll in this course to access the video"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

        try:
            # أHossam Seconed Code of HLS
            # استخدام CloudinaryVideoService لتوليد رابط الوصول الآمن المؤقت
            video_url = CloudinaryVideoService.get_hls_url(
                public_id=lesson.video_public_id
            )

            return Response(
                {
                    "video_url": video_url,
                    "expires_in": 3600,
                    "duration": lesson.video_duration,
                }
            )

        except Exception as e:
            logger.error(f"Failed to generate video URL from Cloudinary: {str(e)}")
            return Response(
                {"error": "Failed to generate video URL", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=True, methods=["delete"])
    def delete_video(self, request, pk=None):
        """
        حذف الفيديو من Cloudinary وقاعدة البيانات
        """
        lesson = self.get_object()

        # التحقق من الصلاحيات
        if lesson.course.instructor != request.user:
            return Response(
                {"error": "You are not authorized to delete this video"},
                status=status.HTTP_403_FORBIDDEN,
            )

        try:
            if lesson.video_public_id:
                # حذف الفيديو من Cloudinary
                CloudinaryVideoService.delete_video(lesson.video_public_id)

                # تحديث قاعدة البيانات
                lesson.video = None
                lesson.video_public_id = None
                lesson.video_duration = None
                lesson.save()
                notify(request.user, VIDEO_DELETED(lesson.title), type=COURSE)
                return Response(
                    {"message": "Video deleted successfully"},
                    status=status.HTTP_204_NO_CONTENT,
                )
            else:
                return Response(
                    {"error": "No video associated with this lesson"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        except Exception as e:
            logger.error(f"Failed to delete video: {str(e)}")
            return Response(
                {"error": "Failed to delete video", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def perform_create(self, serializer):
        lesson = serializer.save()
        # استخلاص public_id إذا تم تعيين حقل video مباشرة (مثلاً يدوياً أو عبر واجهة الإدارة)
        if lesson.video and not lesson.video_public_id:
            public_id = self.extract_public_id_from_url(str(lesson.video))
            if public_id:
                lesson.video_public_id = public_id
                lesson.save()

    def perform_update(self, serializer):
        lesson = serializer.save()
        # إذا تم رفع فيديو جديد يدويًا (اختياري)
        if lesson.video and not lesson.video_public_id:
            public_id = self.extract_public_id_from_url(str(lesson.video))
            if public_id:
                lesson.video_public_id = public_id
                lesson.save()
        return lesson


class QuizViewSet(viewsets.ModelViewSet):
    queryset = Quiz.objects.all()
    serializer_class = QuizSerializer
    permission_classes = [permissions.IsAuthenticated]
    throttle_classes = [QuizGeneralThrottle, QuizSaveAnswerThrottle]

    def _auto_submit_quiz(self, attempt, quiz):
        """دالة مساعدة للتسليم التلقائي عند انتهاء الوقت"""
        if attempt.submitted:
            return  # تم التسليم بالفعل

        answers = getattr(attempt, 'answers', {}) or {}
        score = 0
        correct_count = 0

        # حساب النتيجة بناءً على الإجابات المحفوظة
        for q in quiz.questions.all():
            qid = str(q.id)
            ans_id = answers.get(qid)
            if ans_id:
                try:
                    selected_answer = Answer.objects.get(id=ans_id)
                    if selected_answer.is_correct:
                        score += q.points
                        correct_count += 1
                except Answer.DoesNotExist:
                    pass

        # تحديث المحاولة
        attempt.score = score
        attempt.passed = score >= quiz.passing_score
        attempt.submitted = True
        attempt.submitted_at = timezone.now()
        attempt.save()

        # إصدار شهادة إذا نجح
        if attempt.passed:
            self.issue_certificate(attempt.user, quiz.lesson.course)

    @action(detail=True, methods=["post"])
    def start(self, request, pk=None):
        quiz = self.get_object()
        attempt = UserQuizAttempt.objects.create(
            user=request.user, quiz=quiz, score=0, passed=False
        )
        return Response({"attempt_id": attempt.id})

    @action(detail=True, methods=["post"])
    def submit(self, request, pk=None):
        quiz = self.get_object()
        answers = request.data.get("answers", [])
        # تحقق إذا الطالب سلّم الامتحان قبل كده
        if UserQuizAttempt.objects.filter(user=request.user, quiz=quiz).exists():
            return Response({
                "error": "لقد قمت بتسليم الامتحان بالفعل ولا يمكنك إعادة التسليم."
            }, status=status.HTTP_403_FORBIDDEN)
        # zaki alkholy: log للـ answers القادمة
        print("zaki alkholy - answers received:", answers)
        # حساب النتيجة
        score = 0
        correct_count = 0  # عدد الإجابات الصحيحة
        total_questions = quiz.questions.count()
        # حساب مجموع الدرجات الكلي
        max_score = sum(q.points for q in quiz.questions.all())
        answers_feedback = []
        for answer in answers:
            question_id = answer.get("question_id")
            answer_id = answer.get("answer_id")
            if not question_id or not answer_id:
                continue  # تجاهل الإجابات غير المكتملة
            try:
                question = Question.objects.get(id=question_id)
                selected_answer = Answer.objects.get(id=answer_id)
            except Exception as e:
                print(f"zaki alkholy - invalid question/answer: {e}")
                continue
            is_correct = selected_answer.is_correct
            answers_feedback.append({
                "question_id": question_id,
                "is_correct": is_correct
            })
            if is_correct:
                score += question.points
                correct_count += 1
        # تحديث محاولة الاختبار
        attempt = UserQuizAttempt.objects.create(
            user=request.user,
            quiz=quiz,
            score=score,
            passed=score >= quiz.passing_score,
        )
        # إصدار شهادة إذا نجح في الاختبار (يظل في الباك)
        if attempt.passed:
            self.issue_certificate(request.user, quiz.lesson.course)
            notify(
                request.user,
                QUIZ_CERTIFICATE_CONGRATS(quiz.lesson.course.title),
                type=CERTIFICATE,
            )
        # الاستجابة للفرونت: الدرجة ومجموع الدرجات وقائمة feedback
        return Response({
            "score": score,
            "max_score": max_score,
            "answers_feedback": answers_feedback,
            "correctCount": correct_count,
            "total": total_questions
        })

    def issue_certificate(self, user, course):
        certificate = Certificate.objects.create(
            user=user, course=course, verification_code=str(uuid.uuid4())
        )
        # إشعار عند إصدار الشهادة
        notify(user, CERTIFICATE_ISSUED(course.title), type=CERTIFICATE)
        return certificate

    # إضافة endpoint لإرجاع الوقت المتبقي للامتحان للطالب - zaki alkholy
    @action(detail=True, methods=["get"], url_path="get_time_left")
    def get_time_left(self, request, pk=None):
        quiz = self.get_object()
        user = request.user
        time_limit = quiz.time_limit * 60  # الدقائق إلى ثواني

        # تحقق إذا كان الطالب سلّم الامتحان قبل كده
        submitted_attempt = UserQuizAttempt.objects.filter(
            user=user, quiz=quiz, submitted=True
        ).first()
        if submitted_attempt:
            return Response({
                "time_left": 0,
                "error": "لقد قمت بتسليم الامتحان بالفعل."
            }, status=403)

        # إذا كان time_limit = 0، يعني الواجب بلا وقت محدود
        if time_limit == 0:
            # جلب أو إنشاء محاولة غير مسلمة
            attempt = UserQuizAttempt.objects.filter(
                user=user, quiz=quiz, submitted=False
            ).order_by('-created_at').first()
            if not attempt:
                attempt = UserQuizAttempt.objects.create(
                    user=user, quiz=quiz, score=0, passed=False, submitted=False
                )

            from .serializers import QuestionSerializer
            questions_data = QuestionSerializer(quiz.questions.all(), many=True).data
            return Response({
                "time_left": 999999,  # وقت كبير جداً يعني غير محدود
                "started_at": attempt.created_at,
                "now": timezone.now(),
                "time_limit": 0,  # غير محدود
                "questions": questions_data,
                "unlimited": True  # علامة إن الوقت غير محدود
            })

        # جلب محاولة غير مسلمة
        attempt = UserQuizAttempt.objects.filter(
            user=user, quiz=quiz, submitted=False
        ).order_by('-created_at').first()
        if not attempt:
            attempt = UserQuizAttempt.objects.create(
                user=user, quiz=quiz, score=0, passed=False, submitted=False
            )
            started_at = attempt.created_at
        else:
            started_at = attempt.created_at

        now = timezone.now()
        elapsed = (now - started_at).total_seconds()
        time_left = max(0, int(time_limit - elapsed))
        if time_left <= 0:
            # التسليم التلقائي عند انتهاء الوقت
            self._auto_submit_quiz(attempt, quiz)
            return Response({
                "time_left": 0,
                "auto_submitted": True,
                "message": "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد."
            }, status=200)

        # zaki alkholy: إضافة الأسئلة في الرد
        from .serializers import QuestionSerializer
        questions_data = QuestionSerializer(quiz.questions.all(), many=True).data
        return Response({
            "time_left": time_left,
            "started_at": started_at,
            "now": now,
            "time_limit": time_limit,
            "questions": questions_data  # zaki alkholy
        })

    @action(detail=True, methods=["get"])
    def exam_status(self, request, pk=None):
        quiz = self.get_object()
        user = request.user
        # جلب آخر محاولة للطالب
        attempt = UserQuizAttempt.objects.filter(user=user, quiz=quiz).order_by('-id').first()
        from .serializers import QuestionSerializer
        questions_data = QuestionSerializer(quiz.questions.all(), many=True).data
        if not attempt:
            return Response({
                "status": "not_started",
                "questions": questions_data,
                "quiz_name": quiz.title,
                "time_limit": quiz.time_limit * 60
            })
        if getattr(attempt, 'submitted', False) or attempt.score > 0 or attempt.passed:
            # تم التسليم - إضافة معلومات الإجابات الصحيحة والخاطئة
            answers = getattr(attempt, 'answers', {})
            answers_feedback = []
            # تحويل إجابات الطالب من answer_id إلى index للفرونت إند
            student_answers_by_index = {}

            for q in quiz.questions.all():
                qid = str(q.id)
                selected_answer_id = answers.get(qid)
                is_correct = False
                correct_answer_id = None
                selected_answer_text = None
                correct_answer_text = None
                selected_answer_index = None

                # جلب الإجابة الصحيحة
                correct_answer = q.answers.filter(is_correct=True).first()
                if correct_answer:
                    correct_answer_id = correct_answer.id
                    correct_answer_text = correct_answer.text

                # تحقق من الإجابة المختارة وتحويلها إلى index
                if selected_answer_id:
                    try:
                        selected_answer = Answer.objects.get(id=selected_answer_id)
                        selected_answer_text = selected_answer.text
                        is_correct = selected_answer.is_correct

                        # العثور على index الإجابة المختارة
                        question_answers = list(q.answers.all())
                        for idx, ans in enumerate(question_answers):
                            if ans.id == int(selected_answer_id):
                                selected_answer_index = idx
                                student_answers_by_index[qid] = idx
                                break
                    except (Answer.DoesNotExist, ValueError):
                        pass

                answers_feedback.append({
                    "question_id": qid,
                    "question_text": q.text,
                    "selected_answer_id": selected_answer_id,
                    "selected_answer_text": selected_answer_text,
                    "correct_answer_id": correct_answer_id,
                    "correct_answer_text": correct_answer_text,
                    "is_correct": is_correct,
                    "points": q.points if is_correct else 0
                })

            return Response({
                "status": "submitted",
                "score": attempt.score,
                "max_score": sum(q.points for q in quiz.questions.all()),
                "passed": attempt.passed,
                "quiz_name": quiz.title,
                "answers": student_answers_by_index,  # إرسال الإجابات كـ indices
                "answers_feedback": answers_feedback,
                "questions": questions_data,
                "submitted_at": getattr(attempt, 'submitted_at', None)
            })
        # محاولة جارية
        return Response({
            "status": "in_progress",
            "answers": getattr(attempt, 'answers', {}),
            "questions": questions_data,
            "quiz_name": quiz.title,
            "started_at": getattr(attempt, 'created_at', None),
            "time_limit": quiz.time_limit * 60
        })

    @action(detail=True, methods=["post"])
    def start(self, request, pk=None):
        quiz = self.get_object()
        user = request.user
        # لا تنشئ محاولة جديدة إذا هناك محاولة جارية أو تم التسليم
        attempt = UserQuizAttempt.objects.filter(user=user, quiz=quiz).order_by('-id').first()
        if attempt and (getattr(attempt, 'submitted', False) or attempt.score > 0 or attempt.passed):
            return Response({"error": "تم تسليم الامتحان بالفعل"}, status=403)
        if not attempt:
            attempt = UserQuizAttempt.objects.create(user=user, quiz=quiz, score=0, passed=False, answers={})
        return Response({"attempt_id": attempt.id})

    @action(detail=True, methods=["patch"])
    def save_answer(self, request, pk=None):
        quiz = self.get_object()
        user = request.user
        question_id = request.data.get("question_id")
        answer_id = request.data.get("answer_id")
        attempt = UserQuizAttempt.objects.filter(user=user, quiz=quiz, score=0, passed=False).order_by('-id').first()
        if not attempt:
            return Response({"error": "لا توجد محاولة جارية"}, status=400)
        answers = getattr(attempt, 'answers', {}) or {}
        answers[str(question_id)] = answer_id
        attempt.answers = answers
        attempt.save()
        return Response({"message": "تم حفظ الإجابة", "answers": answers})

    @action(detail=True, methods=["post"])
    def submit(self, request, pk=None):
        quiz = self.get_object()
        user = request.user
        # جلب المحاولة الجارية فقط
        attempt = UserQuizAttempt.objects.filter(user=user, quiz=quiz, score=0, passed=False).order_by('-id').first()
        if not attempt:
            return Response({"error": "لا توجد محاولة جارية أو تم التسليم بالفعل"}, status=403)
        answers = getattr(attempt, 'answers', {}) or {}
        # حساب النتيجة
        score = 0
        correct_count = 0
        total_questions = quiz.questions.count()
        max_score = sum(q.points for q in quiz.questions.all())
        answers_feedback = []
        for q in quiz.questions.all():
            qid = str(q.id)
            ans_id = answers.get(qid)
            is_correct = False
            if ans_id:
                try:
                    selected_answer = Answer.objects.get(id=ans_id)
                    is_correct = selected_answer.is_correct
                except:
                    pass
            answers_feedback.append({"question_id": qid, "is_correct": is_correct})
            if is_correct:
                score += q.points
                correct_count += 1
        attempt.score = score
        attempt.passed = score >= quiz.passing_score
        attempt.submitted = True
        attempt.submitted_at = timezone.now()
        attempt.save()
        # إصدار شهادة إذا نجح
        if attempt.passed:
            self.issue_certificate(user, quiz.lesson.course)
            notify(user, QUIZ_CERTIFICATE_CONGRATS(quiz.lesson.course.title), type=CERTIFICATE)
        return Response({
            "score": score,
            "max_score": max_score,
            "answers_feedback": answers_feedback,
            "correctCount": correct_count,
            "total": total_questions,
            "passed": attempt.passed
        })


def video_player(request, lesson_id):
    lesson = get_object_or_404(Lesson, id=lesson_id)
    video_url = CloudinaryVideoService.generate_secure_url(
        public_id=lesson.video_public_id, expiration=3600
    )
    return render(
        request, "main/video_player.html", {"lesson": lesson, "video_url": video_url}
    )


class CertificateViewSet(viewsets.ModelViewSet):
    queryset = Certificate.objects.all()
    serializer_class = CertificateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


class AnnouncementViewSet(viewsets.ModelViewSet):
    queryset = Announcement.objects.all()
    serializer_class = AnnouncementSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        course_id = self.request.query_params.get("course_id")
        if course_id:
            return self.queryset.filter(course_id=course_id)
        return self.queryset.none()

    def perform_create(self, serializer):
        announcement = serializer.save()
        course = announcement.course
        students = list(course.students.all())
        send_bulk_notification.delay(
            [student.id for student in students],
            ANNOUNCEMENT_NEW(course.title, announcement.title),
            type=COURSE,
        )


class FAQViewSet(viewsets.ModelViewSet):
    queryset = FAQ.objects.all()
    serializer_class = FAQSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        course_id = self.request.query_params.get("course_id")
        if course_id:
            return self.queryset.filter(course_id=course_id)
        return self.queryset.none()


class VideoPlayerView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, lesson_id):
        try:
            lesson = get_object_or_404(Lesson, id=lesson_id)

            # التحقق من صلاحية الوصول للدرس
            if not lesson.is_preview:
                enrollment = Enrollment.objects.filter(
                    student=request.user, course=lesson.course
                ).first()

                if not enrollment:
                    return Response(
                        {"error": "يجب عليك الاشتراك في الدورة أولاً"},
                        status=status.HTTP_403_FORBIDDEN,
                    )

            # إنشاء توكن للوصول للفيديو
            token = self.get_video_token(request.user.id, lesson_id)
            video_url = f"{lesson.video_url}?token={token}"

            # تحديث تقدم المشاهدة
            if not lesson.is_preview:
                enrollment = Enrollment.objects.get(
                    student=request.user, course=lesson.course
                )
                enrollment.last_accessed = timezone.now()
                enrollment.current_lesson = lesson
                enrollment.save()

            return render(
                request,
                "main/video_player.html",
                {"lesson": lesson, "video_url": video_url, "user": request.user},
            )
        except Exception as e:
            logger.error(f"Video player view failed: {str(e)}")
            return Response(
                {"error": "حدث خطأ أثناء تحميل الفيديو"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_video_token(self, user_id, lesson_id):
        timestamp = int(time.time())
        data = f"{user_id}:{lesson_id}:{timestamp}"
        secret = settings.SECRET_KEY
        token = hmac.new(
            secret.encode("utf-8"), data.encode("utf-8"), hashlib.sha256
        ).hexdigest()
        return f"{token}:{timestamp}"


class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        print("REGISTER REQUEST DATA:", request.data)  # Debug logging
        serializer = UserSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "user": UserSerializer(user).data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "token": str(
                        refresh.access_token
                    ),  # Add token alias for frontend compatibility
                },
                status=status.HTTP_201_CREATED,
            )
        print("REGISTER ERRORS:", serializer.errors)  # Debug logging
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        username = request.data.get("username")
        password = request.data.get("password")

        user = authenticate(username=username, password=password)

        if user:
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "user": UserSerializer(user, context={"request": request}).data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                }
            )
        return Response(
            {"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED
        )


class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        return Category.objects.all()


class UserListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            # جلب جميع المستخدمين
            users = User.objects.all()

            # التحقق من وجود مستخدمين
            if not users.exists():
                return Response(
                    {"message": "لا يوجد مستخدمين في النظام", "users": []},
                    status=status.HTTP_200_OK,
                )

            # تحويل البيانات إلى القائمة المطلوبة
            user_data = []
            for user in users:
                user_info = {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                user_data.append(user_info)

            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class GetUsersView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        try:
            users = User.objects.all()
            user_data = [
                {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "is_instructor": user.is_instructor,
                    "is_student": user.is_student,
                    "profile_image": (
                        user.profile_image.url if user.profile_image else None
                    ),
                    "phone_number": user.phone_number,
                    "bio": user.bio,
                    "date_joined": user.date_joined,
                }
                for user in users
            ]
            return Response(
                {
                    "message": "تم جلب المستخدمين بنجاح",
                    "count": len(user_data),
                    "users": user_data,
                }
            )
        except Exception as e:
            return Response(
                {"message": "حدث خطأ أثناء جلب المستخدمين", "error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


class InstructorProfileViewSet(viewsets.ModelViewSet):
    queryset = InstructorProfile.objects.all()
    serializer_class = InstructorProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return self.queryset.filter(user=self.request.user)


class ReviewViewSet(viewsets.ModelViewSet):
    queryset = Review.objects.all()  # يشمل كل التعليقات
    serializer_class = ReviewSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        # إذا كان الطلب list فقط أظهر المعتمدة، غير ذلك أظهر الكل
        if self.action == "list":
            return Review.objects.filter(is_approved=True)
        return Review.objects.all()

    def perform_create(self, serializer):
        serializer.save(user=self.request.user, is_approved=False)

    @action(detail=True, methods=["get"], url_path="comments")
    def comments(self, request, pk=None):
        review = self.get_object()
        comments = review.comments.filter(parent__isnull=True).order_by("created_at")
        serializer = ReviewSerializer(comments, many=True, context={"request": request})
        return Response(serializer.data)

    @action(detail=True, methods=["post"], url_path="add_comment")
    def add_comment(self, request, pk=None):
        review = self.get_object()
        text = request.data.get("text")
        parent_id = request.data.get("parent")
        parent = None
        if parent_id:
            try:
                parent = ReviewComment.objects.get(id=parent_id, review=review)
            except ReviewComment.DoesNotExist:
                return Response({"error": "Parent comment not found"}, status=404)
        comment = ReviewComment.objects.create(
            review=review, user=request.user, text=text, parent=parent
        )
        if parent:
            # إشعار لصاحب التعليق الأصلي
            notify(parent.user, REVIEW_COMMENT_REPLY(review.course.title), type=REVIEW)
        else:
            # إشعار لصاحب المراجعة
            notify(review.user, REVIEW_COMMENT_NEW(review.course.title), type=REVIEW)
        serializer = ReviewSerializer(comment, context={"request": request})
        return Response(serializer.data, status=201)

    @action(
        detail=True,
        methods=["patch"],
        url_path="edit_comment",
        permission_classes=[IsAuthenticated],
    )
    def edit_comment(self, request, pk=None):
        try:
            comment = ReviewComment.objects.get(id=pk, user=request.user)
            comment.text = request.data.get("text", comment.text)
            comment.save()
            # إرسال إشعار لصاحب المراجعة إذا كان التعليق بدون رد
            if comment.parent is None:
                notify(
                    comment.review.user,
                    REVIEW_COMMENT_EDIT(
                        request.user.username, comment.review.course.title
                    ),
                    type=REVIEW,
                )
            # أو لصاحب التعليق الأصلي إذا كان هذا رد
            else:
                notify(
                    comment.parent.user,
                    REVIEW_REPLY_EDIT(
                        request.user.username, comment.review.course.title
                    ),
                    type=REVIEW,
                )
            return Response({"message": "تم تعديل التعليق بنجاح"}, status=200)
        except ReviewComment.DoesNotExist:
            return Response({"error": "التعليق غير موجود أو غير مسموح"}, status=404)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_comment",
        permission_classes=[IsAuthenticated],
    )
    def delete_comment(self, request, pk=None):
        try:
            comment = ReviewComment.objects.get(id=pk, user=request.user)
            recipient = comment.parent.user if comment.parent else comment.review.user
            course_title = comment.review.course.title
            comment.delete()
            notify(
                recipient,
                REVIEW_COMMENT_DELETE(request.user.username, course_title),
                type=REVIEW,
            )
            return Response({"message": "تم حذف التعليق بنجاح"}, status=204)
        except ReviewComment.DoesNotExist:
            return Response({"error": "التعليق غير موجود أو غير مسموح"}, status=404)

    @action(
        detail=True,
        methods=["patch"],
        url_path="edit_review",
        permission_classes=[IsAuthenticated],
    )
    def edit_review(self, request, pk=None):
        review = self.get_object()
        if review.user != request.user:
            return Response({"error": "غير مصرح لك بتعديل هذه المراجعة"}, status=403)
        review.text = request.data.get("text", review.text)
        review.rating = request.data.get("rating", review.rating)
        review.save()
        notify(
            review.course.instructor,
            REVIEW_EDIT(request.user.username, review.course.title),
            type=REVIEW,
        )
        return Response({"message": "تم تعديل المراجعة بنجاح"}, status=200)

    @action(
        detail=True,
        methods=["delete"],
        url_path="delete_review",
        permission_classes=[IsAuthenticated],
    )
    def delete_review(self, request, pk=None):
        review = self.get_object()
        if review.user != request.user:
            return Response({"error": "غير مصرح لك بالحذف"}, status=403)
        course_title = review.course.title
        instructor = review.course.instructor
        review.delete()
        notify(
            instructor, REVIEW_DELETE(request.user.username, course_title), type=REVIEW
        )
        return Response({"message": "تم حذف المراجعة بنجاح"}, status=204)

    @action(
        detail=True,
        methods=["patch"],
        url_path="approve_review",
        permission_classes=[IsAuthenticated],
    )
    def approve_review(self, request, pk=None):
        if not request.user.is_staff:
            return Response({"error": "صلاحيات مطلوبة"}, status=403)
        review = self.get_object()
        review.is_approved = True
        review.save()
        notify(review.user, REVIEW_APPROVED(review.course.title), type=REVIEW)
        return Response({"message": "تمت الموافقة بنجاح"}, status=200)


class InstructorAvailabilityViewSet(viewsets.ModelViewSet):
    queryset = InstructorAvailability.objects.all()
    serializer_class = InstructorAvailabilitySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        user_id = self.request.query_params.get("user_id")
        date_param = self.request.query_params.get("date")
        queryset = self.queryset
        # إذا كان الأدمن وأرسل user_id، يرجع مواعيد هذا المستخدم
        if user.is_staff and user_id:
            queryset = queryset.filter(user_id=user_id)
        else:
            queryset = queryset.filter(user=user)
        # فلترة حسب اليوم إذا تم إرسال باراميتر date
        if date_param:
            import datetime

            try:
                date_obj = datetime.datetime.strptime(date_param, "%Y-%m-%d")
                day_name = date_obj.strftime("%A").lower()
                queryset = queryset.filter(day=day_name)
            except Exception as e:
                pass
        return queryset

    def perform_create(self, serializer):
        user = self.request.user
        user_id = self.request.data.get("user_id")
        # الأدمن يقدر يحدد user_id، المدرس لا
        if user.is_staff and user_id:
            serializer.save(user_id=user_id)
        else:
            serializer.save(user=user)


from rest_framework import viewsets, permissions
from .models import Notification


class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user).order_by(
            "-created_at"
        )

    @action(detail=True, methods=["patch"], url_path="mark_as_read")
    def mark_as_read(self, request, pk=None):
        notification = self.get_object()
        if notification.is_read:
            return Response({"message": "الإشعار مقروء بالفعل"})
        notification.is_read = True
        notification.save()
        return Response({"message": "تم تعليم الإشعار كمقروء"})


class InstructorDetailAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, id):
        try:
            instructor = User.objects.get(id=id, is_instructor=True)
            data = InstructorWithCoursesSerializer(
                instructor, context={"request": request}
            ).data
            return Response(data)
        except User.DoesNotExist:
            return Response({"detail": "Instructor not found."}, status=404)
        except Exception as e:
            import traceback

            print("API DEBUG | InstructorDetailAPIView error:", traceback.format_exc())
            return Response({"detail": str(e)}, status=500)


class InstructorDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        total_courses = user.taught_courses.count()
        active_courses = user.taught_courses.filter(is_published=True).count()
        student_ids = set()
        for course in user.taught_courses.all():
            student_ids.update(course.students.values_list("id", flat=True))
        total_students = len(student_ids)
        total_revenue = (
            Order.objects.filter(course__instructor=user, status="completed").aggregate(
                Sum("amount")
            )["amount__sum"]
            or 0
        )
        return Response(
            {
                "total_courses": total_courses,
                "active_courses": active_courses,
                "total_students": total_students,
                "total_revenue": float(total_revenue),
            }
        )


class QuestionViewSet(viewsets.ModelViewSet):
    queryset = Question.objects.all()
    serializer_class = QuestionSerializer
    permission_classes = [permissions.IsAuthenticated]

class AnswerViewSet(viewsets.ModelViewSet):
    queryset = Answer.objects.all()
    serializer_class = AnswerSerializer
    permission_classes = [permissions.IsAuthenticated]
