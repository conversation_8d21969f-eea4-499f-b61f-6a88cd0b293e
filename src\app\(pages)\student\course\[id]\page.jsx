"use client";
import React, { useEffect, useState, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../../../store/authSlice";
import axios from "axios";
import Link from "next/link";
import Cookies from "js-cookie";
import Image from "next/image";
import dynamic from "next/dynamic";
import PaymentForm from "../../../../_Components/PaymentForm";
import "plyr/dist/plyr.css";
import { useForm } from "react-hook-form";
import {
  fetchStudentCourse,
  fetchCourseLessons,
  fetchCourseReviews,
  submitCourseReview,
  fetchReviewComments,
  toggleCourseLike,
  toggleCommentLike,
  addReviewReply,
  fetchExamTimeLeft,
  fetchExamStatus, // جديد
  saveExamAnswer, // جديد
  submitExam, // جديد
} from "../../../../../services/student";
import { API_BASE_URL } from '../../../../../config/api';

// Dynamic import for Plyr
const Plyr = dynamic(() => import("plyr"), { ssr: false });
const PlyrCSS = dynamic(() => import("plyr/dist/plyr.css"), { ssr: false });

// مودال React بسيط بدون مكتبة خارجية (تصحيح إغلاق الوسوم)
function ExamAssignmentModal({ open, onClose, type, id, duration, questions: propQuestions, examStatus, onExamSubmitted }) {
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [timer, setTimer] = useState(duration || 0);
  const [fetchError, setFetchError] = useState(null); // جديد
  const [feedbackMap, setFeedbackMap] = useState({}); // جديد: feedback لكل سؤال

  // إذا تم تمرير الأسئلة من props، استخدمها مباشرة
  useEffect(() => {
    if (open) {
      setResult(null);
      setFetchError(null);
      setFeedbackMap({});

      // تحقق إذا كان examStatus يشير إلى أن الامتحان اتسلم
      if (examStatus && examStatus.status === 'submitted') {
        // عرض النتيجة من examStatus
        setResult({
          message: `تم تسليم الامتحان بنجاح! النتيجة: ${examStatus.score}/${examStatus.max_score}`,
          correctCount: examStatus.answers_feedback ? examStatus.answers_feedback.filter(f => f.is_correct).length : 0,
          score: examStatus.score,
          max_score: examStatus.max_score,
          answers_feedback: examStatus.answers_feedback || [],
          passed: examStatus.passed
        });
        setQuestions(examStatus.questions || []);
        setAnswers(examStatus.answers || {});
        setLoading(false);
        return;
      }

      // تحقق إذا تم التسليم مسبقًا من الكوكيز
      const submittedKey = `exam_${id}_submitted`;
      if (Cookies.get(submittedKey)) {
        setResult({
          message: "تم تسليم الامتحان بالفعل ولا يمكنك إعادة التسليم.",
          correctCount: null,
          score: null,
          max_score: null,
          answers_feedback: [],
        });
        setQuestions([]);
        setLoading(false);
        setAnswers({});
        return;
      }
      // تحميل الإجابات المحفوظة من الباك إند أو الكوكيز - zaki alkholy
      const answersKey = `exam_${id}_answers`;

      // محاولة جلب الإجابات من الباك إند أولاً - zaki alkholy
      if (type === "exam") {
        // استخدام دالة async منفصلة - zaki alkholy
        (async () => {
          try {
            const token = Cookies.get("authToken");
            const timeRes = await fetchExamTimeLeft(id, token);
            if (timeRes.saved_answers) {
              // تحويل الإجابات من answer_id إلى index - zaki alkholy
              const convertedAnswers = {};
              Object.keys(timeRes.saved_answers).forEach(qid => {
                const answerId = timeRes.saved_answers[qid];
                const question = propQuestions?.find(q => q.id == qid);
                if (question && question.answers) {
                  const answerIndex = question.answers.findIndex(a => a.id == answerId);
                  if (answerIndex !== -1) {
                    convertedAnswers[qid] = answerIndex;
                  }
                }
              });
              setAnswers(convertedAnswers);
              console.log('zaki alkholy - تم تحميل الإجابات من الباك إند:', convertedAnswers);
            }
          } catch (e) {
            console.log('لم يتم العثور على إجابات محفوظة في الباك إند');
          }
        })();
      }

      // تحميل الإجابات من الكوكيز كـ fallback - zaki alkholy
      try {
        const raw = Cookies.get(answersKey);
        if (raw) {
          const cookieAnswers = JSON.parse(raw);
          setAnswers(prev => ({ ...cookieAnswers, ...prev })); // دمج مع الإجابات من الباك إند
        }
      } catch {}

      console.log('zaki alkholy - تم تحميل الإجابات المحفوظة');

      // استخدم الأسئلة من props إذا توفرت
      if (Array.isArray(propQuestions) && propQuestions.length > 0) {
        setQuestions(propQuestions);
        setLoading(false);
      } else {
        setQuestions([]);
        setLoading(false);
        setFetchError("لا توجد أسئلة متاحة لهذا الامتحان/الواجب");
      }
      if (type === "exam" && duration) setTimer(duration);
    }
  }, [open, type, id, duration, propQuestions]);

  // عند تغيير answers: خزّنها في الكوكيز (طالما لم يتم التسليم)
  useEffect(() => {
    if (!open || result) return;
    const answersKey = `exam_${id}_answers`;
    Cookies.set(answersKey, JSON.stringify(answers), { expires: 1 });
  }, [answers, open, result, id]);

  // عند التسليم الناجح: خزّن علامة التسليم وامسح الإجابات
  useEffect(() => {
    if (result && !fetchError && open) {
      const submittedKey = `exam_${id}_submitted`;
      const answersKey = `exam_${id}_answers`;
      Cookies.set(submittedKey, "1", { expires: 7 });
      Cookies.remove(answersKey);
    }
  }, [result, fetchError, open, id]);

  useEffect(() => {
    if (type !== "exam" || !open || !duration || result) return;
    if (timer === 0) {
      // التسليم التلقائي عند انتهاء الوقت
      handleAutoSubmit();
      return;
    }
    const interval = setInterval(() => setTimer((t) => t - 1), 1000);
    return () => clearInterval(interval);
  }, [timer, open, type, duration, result]);

  // عند استلام نتيجة التصحيح، جهز feedbackMap
  useEffect(() => {
    if (result && result.answers_feedback) {
      // answers_feedback: [{question_id, is_correct, correct_answer_id, selected_answer_id}]
      const map = {};
      result.answers_feedback.forEach(fb => {
        map[fb.question_id] = {
          is_correct: fb.is_correct,
          correct_answer_id: fb.correct_answer_id,
          selected_answer_id: fb.selected_answer_id,
          correct_answer_text: fb.correct_answer_text,
          selected_answer_text: fb.selected_answer_text
        };
      });
      setFeedbackMap(map);
    }
  }, [result]);

  // تحديث handleChange ليعمل بشكل متزامن مع setAnswers
  const handleChange = (qid, choiceIdx) => {
    setAnswers((prev) => {
      const updated = { ...prev, [qid]: choiceIdx };
      // إرسال الإجابة للباك اند
      (async () => {
        try {
          const token = Cookies.get("authToken");
          const q = questions.find(q => q.id == qid);
          let answer_id = null;
          if (q && Array.isArray(q.answers) && q.answers[choiceIdx]) {
            answer_id = q.answers[choiceIdx].id;
          }
          // Log توضيحي
          console.log('DEBUG: qid:', qid, 'choiceIdx:', choiceIdx, 'answer_id:', answer_id, 'q:', q);
          await saveExamAnswer(id, qid, answer_id, token);
        } catch (e) { console.error('saveExamAnswer error:', e); }
      })();
      return updated;
    });
  };

  const handleSubmit = async () => {
    if (submitting || result || fetchError || questions.length === 0) return;
    setSubmitting(true);
    const token = Cookies.get("authToken");
    try {
      const res = await submitExam(id, token);
      setResult(res);
      // استدعاء callback لتحديث حالة الامتحان في الصفحة الرئيسية
      if (onExamSubmitted) {
        onExamSubmitted(id);
      }
    } catch (err) {
      // طباعة الخطأ الحقيقي من الباك اند
      if (err?.response && err.response.data) {
        alert(err.response.data.detail || err.response.data.error || JSON.stringify(err.response.data));
        console.error('تفاصيل خطأ التسليم:', err.response.data);
      } else {
        alert("حدث خطأ أثناء التسليم");
        console.error('خطأ غير معروف أثناء التسليم:', err);
      }
    }
    setSubmitting(false);
  };

  const handleAutoSubmit = async () => {
    if (submitting || result) return;
    setSubmitting(true);
    const token = Cookies.get("authToken");

    console.log('zaki alkholy - بدء التسليم التلقائي للامتحان:', id);

    try {
      // محاولة التسليم العادي أولاً - zaki alkholy
      const res = await submitExam(id, token);
      setResult({
        ...res,
        message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
        auto_submitted: true
      });

      console.log('zaki alkholy - تم التسليم التلقائي بنجاح:', res);

      if (onExamSubmitted) {
        onExamSubmitted(id);
      }
    } catch (err) {
      console.error('zaki alkholy - خطأ في التسليم التلقائي:', err);

      // في حالة فشل التسليم، جرب جلب النتيجة من الباك إند - zaki alkholy
      try {
        const statusRes = await fetchExamStatus(id, token);
        if (statusRes.status === 'submitted') {
          setResult({
            message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
            auto_submitted: true,
            score: statusRes.score,
            max_score: statusRes.max_score,
            answers_feedback: statusRes.answers_feedback || [],
            correctCount: statusRes.answers_feedback ? statusRes.answers_feedback.filter(f => f.is_correct).length : 0
          });
        } else {
          // إذا لم يتم التسليم، اعرض رسالة انتهاء الوقت
          setResult({
            message: "انتهى وقت الامتحان. تم حفظ إجاباتك المتاحة.",
            auto_submitted: true,
            score: 0,
            max_score: questions.length * 10,
            answers_feedback: []
          });
        }
      } catch (statusErr) {
        console.error('zaki alkholy - خطأ في جلب حالة الامتحان:', statusErr);
        setResult({
          message: "انتهى وقت الامتحان. تم حفظ إجاباتك المتاحة.",
          auto_submitted: true,
          score: 0,
          max_score: questions.length * 10,
          answers_feedback: []
        });
      }
    }
    setSubmitting(false);
  };

  const handleClose = () => {
    setResult(null);
    setAnswers({});
    setFetchError(null);
    setQuestions([]);
    setFeedbackMap({});
    onClose();
  };

  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative animate-fadeIn">
        <button
          onClick={handleClose}
          className="absolute left-4 top-4 text-gray-400 hover:text-gray-700 text-2xl"
          aria-label="إغلاق"
        >
          ×
        </button>
        <h2 className="text-xl font-bold mb-4 text-center">
          {type === "exam" ? "امتحان" : "واجب"} الكورس
        </h2>
        {loading ? (
          <div className="text-center py-8">جاري التحميل...</div>
        ) : fetchError ? (
          <div className="text-center text-red-600 py-8">{fetchError}</div>
        ) : result ? (
          <div className="text-center space-y-4 py-6">
            {result.auto_submitted && (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded mb-4">
                ⏰ تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد
              </div>
            )}
            <div>
              <b>عدد الإجابات الصحيحة:</b> {result.correctCount}
            </div>
            <div>
              <b>الدرجة النهائية:</b> {result.score} / {result.max_score}
            </div>
            <div>
              <b>النتيجة:</b> {result.message}
            </div>
            {/* عرض تصحيح الأسئلة مع التلوين */}
            <div className="mt-6 text-right">
              {questions.map((q, idx) => {
                const feedback = feedbackMap[q.id];
                const isCorrect = feedback?.is_correct || false;
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                const selectedIdx = answers[q.id];

                // استخراج index الإجابة الصحيحة من feedback أو من الأسئلة
                let correctIdx = null;
                if (feedback?.correct_answer_id && Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.id === feedback.correct_answer_id);
                } else if (Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.is_correct);
                } else if (Array.isArray(q.choices) && q.choices.findIndex) {
                  // لو choices فيها is_correct
                  correctIdx = q.choices.findIndex(c => c.is_correct);
                }
                return (
                  <div
                    key={q.id}
                    className={`border rounded p-4 mb-2 ${
                      isCorrect === true
                        ? 'bg-green-50 border-green-400'
                        : isCorrect === false
                        ? 'bg-red-50 border-red-400'
                        : ''
                    }`}
                  >
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    <div className="space-y-2">
                      {choices.map((choice, i) => {
                        const isSelected = selectedIdx === i;
                        // هل هذا هو الاختيار الصحيح؟
                        const isRight = correctIdx === i;
                        let choiceClass = '';
                        if (isSelected && isCorrect === true) choiceClass = 'text-green-700 font-bold';
                        else if (isSelected && isCorrect === false) choiceClass = 'text-red-700 font-bold';
                        // إبراز الإجابة الصحيحة إذا الطالب أخطأ
                        if (isCorrect === false && isRight) choiceClass = 'bg-green-100 text-green-800 font-bold rounded px-2 py-1';
                        return (
                          <div key={i} className={choiceClass + ' flex items-center gap-2'}>
                            <input
                              type="radio"
                              name={`q_${q.id}`}
                              value={i}
                              checked={isSelected}
                              readOnly
                              disabled
                            />{' '}
                            <span>{choice}</span>
                            {/* علامة صح بجانب الإجابة الصحيحة إذا الطالب أخطأ */}
                            {isCorrect === false && isRight && (
                              <span className="ml-2 text-green-600 text-lg font-bold">✔</span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                    {isCorrect === true && (
                      <div className="mt-2 text-green-700 font-bold">إجابة صحيحة</div>
                    )}
                    {isCorrect === false && (
                      <div className="mt-2 text-red-700 font-bold">إجابة خاطئة</div>
                    )}
                  </div>
                );
              })}
            </div>
            <button onClick={handleClose} className="mt-4 bg-blue-600 text-white px-6 py-2 rounded">إغلاق</button>
          </div>
        ) : (
          <>
            {type === "exam" && (
              <div className="flex justify-end mb-2">
                <span className="bg-gray-100 px-3 py-1 rounded text-sm font-bold text-red-600">
                  الوقت المتبقي: {formatTime(timer)}
                </span>
              </div>
            )}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit();
              }}
              className="space-y-6"
            >
              {questions.map((q, idx) => {
                // دعم الأسئلة القادمة من الداتا (answers) أو من API (choices)
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                return (
                  <div key={q.id} className="border rounded p-4 mb-2">
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    <div className="space-y-2">
                      {choices.map((choice, i) => (
                        <label key={i} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name={`q_${q.id}`}
                            value={i}
                            checked={answers[q.id] === i}
                            onChange={() => handleChange(q.id, i)}
                            disabled={!!result || (type === "exam" && timer === 0)}
                          />
                          <span>{choice}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                );
              })}
              <div className="flex gap-2 justify-center mt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded disabled:opacity-50"
                  disabled={submitting || (type === "exam" && timer === 0)}
                >
                  {submitting ? "جاري التسليم..." : "تسليم"}
                </button>
                <button
                  type="button"
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded"
                  onClick={handleClose}
                  disabled={submitting}
                >
                  إغلاق
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}

export default function CoursePage() {
  const { id } = useParams();
  const router = useRouter();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const user = useSelector(selectCurrentUser);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [previewVideoUrl, setPreviewVideoUrl] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [player, setPlayer] = useState(null);
  const videoRef = useRef(null);
  const [isClient, setIsClient] = useState(false);
  const [reviews, setReviews] = useState([]);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [reviewError, setReviewError] = useState(null);
  const [reviewSuccess, setReviewSuccess] = useState(null);
  const { register, handleSubmit, reset } = useForm();

  // إضافة حالة الإعجاب وعدد الإعجابات
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  // إضافة حالة تحميل زر الإعجاب
  const [likeLoading, setLikeLoading] = useState(false);

  // إضافة ref لمنع إعادة تعيين isLiked إلا عند أول تحميل الكورس
  const didInitLike = useRef(false);

  // Reset states when course ID changes
  useEffect(() => {
    setCourse(null);
    setLessons([]);
    setLoading(true);
    setError(null);
    setIsEnrolled(false);
    setPreviewVideoUrl(null);
    setPreviewLoading(false);
    setPreviewError(null);
    setSelectedLesson(null);
    setPlayer(null);
    setIsLiked(false);
    setLikesCount(0);
  }, [id]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const getImageUrl = (path) => {
    if (!path) return null;

    if (path.startsWith("http://") || path.startsWith("https://")) {
      return path;
    }

    return `${API_BASE_URL}${path.startsWith("/") ? path : `/${path}`}`;
  };

  const getProfileImageUrl = (path) => {
    if (!path) return null;

    if (path.startsWith("http://") || path.startsWith("https://")) {
      return path;
    }

    return `${API_BASE_URL}${path.startsWith("/") ? path : `/${path}`}`;
  };

  // جلب بيانات الكورس
  useEffect(() => {
    const controller = new AbortController();
    const loadCourseData = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول للوصول إلى هذه الصفحة");
          router.push("/login");
          return;
        }
        const courseData = await fetchStudentCourse(id, token, controller.signal);
        const lessonsData = await fetchCourseLessons(id, token, controller.signal);
        setCourse(courseData);
        setLessons(lessonsData);
        if (user && courseData.students) {
          const enrolled = courseData.students.some(s => s.id === user.id);
          setIsEnrolled(enrolled);
        } else {
          setIsEnrolled(false);
        }
        if (!didInitLike.current) {
          setIsLiked(!!courseData.is_liked);
          setLikesCount(courseData.likes_count || 0);
          didInitLike.current = true;
        }
        setLoading(false);
      } catch (err) {
        if (err.name === "AbortError") return;
        if (!controller.signal.aborted) {
          if (err.response?.status === 401) {
            Cookies.remove("authToken");
            setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
            router.push("/login");
          } else {
            setError(err.response?.data?.message || "حدث خطأ أثناء تحميل الكورس");
          }
          setLoading(false);
        }
      }
    };
    loadCourseData();
    return () => controller.abort();
  }, [id, router, user]);

  // إعادة تعيين ref عند تغيير الكورس
  useEffect(() => {
    didInitLike.current = false;
  }, [id]);

  const handleVideo = async (lesson) => {
    setPreviewLoading(true);
    setPreviewError(null);
    setSelectedLesson(lesson);

    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setPreviewError("يرجى تسجيل الدخول أولاً");
        return;
      }

      const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      const response = await axios.get(
        `${API_BASE_URL}/api/lessons/${lesson.id}/video_url/`,
        {
          headers,
          timeout: 10000,
          validateStatus: function (status) {
            return status >= 200 && status < 500;
          },
        }
      );

      if (response.data.video_url) {
        setPreviewVideoUrl(response.data.video_url);
      } else if (response.data.video) {
        setPreviewVideoUrl(response.data.video);
      } else {
        setPreviewError("لم يتم العثور على رابط الفيديو");
      }
    } catch (err) {
      console.error("Error fetching video:", err);

      if (err.code === "ECONNABORTED") {
        setPreviewError("انتهت مهلة الاتصال بالخادم");
      } else if (err.response) {
        console.error("Error response:", err.response.data);
        console.error("Error status:", err.response.status);

        if (err.response.status === 403) {
          setPreviewError("ليس لديك صلاحية لمشاهدة هذا الفيديو");
        } else if (err.response.status === 401) {
          setPreviewError("يرجى تسجيل الدخول أولاً");
        } else {
          setPreviewError(`خطأ في الخادم: ${err.response.status}`);
        }
      } else if (err.request) {
        console.error("Error request:", err.request);
        setPreviewError("لا يمكن الاتصال بالخادم");
      } else {
        console.error("Error message:", err.message);
        setPreviewError("حدث خطأ أثناء تحميل الفيديو");
      }
    } finally {
      setPreviewLoading(false);
    }
  };

  // Initialize Plyr when video URL is available
  useEffect(() => {
    if (!isClient) return;

    if (previewVideoUrl && videoRef.current) {
      if (player) {
        player.destroy();
      }

      const initPlyr = async () => {
        const PlyrModule = await import("plyr");
        const newPlayer = new PlyrModule.default(videoRef.current, {
          controls: [
            "play-large",
            "play",
            "progress",
            "current-time",
            "mute",
            "volume",
            "captions",
            "settings",
            "pip",
            "airplay",
            "fullscreen",
          ],
          download: false,
          hideControls: true,
          keyboard: { focused: true, global: true },
          tooltips: { controls: true, seek: true },
          captions: { active: true, language: "auto", update: true },
          quality: {
            default: 720,
            options: [4320, 2880, 2160, 1440, 1080, 720, 576, 480, 360, 240],
          },
        });

        setPlayer(newPlayer);
      };

      initPlyr();

      return () => {
        if (player) {
          player.destroy();
        }
      };
    }
  }, [previewVideoUrl, isClient]);

  // جلب التعليقات عند تحميل الصفحة أو تغيير الكورس
  useEffect(() => {
    if (!id) return;
    setReviewLoading(true);
    fetchCourseReviews(id)
      .then(setReviews)
      .catch(() => setReviews([]))
      .finally(() => setReviewLoading(false));
  }, [id]);

  // إرسال تعليق جديد
  const onSubmitReview = async (data) => {
    setReviewError(null);
    setReviewSuccess(null);
    try {
      const token = Cookies.get("authToken");
      await submitCourseReview(id, data, token);
      setReviewSuccess("تم إرسال التقييم بنجاح وسيظهر بعد المراجعة.");
      reset();
      const res = await fetchCourseReviews(id);
      setReviews(res);
    } catch (err) {
      setReviewError("حدث خطأ أثناء إرسال التقييم");
    }
  };

  // دالة تبديل الإعجاب
  const handleToggleLike = async () => {
    if (!user) {
      router.push("/login");
      return;
    }
    try {
      setLikeLoading(true);
      const token = Cookies.get("authToken");
      const res = await toggleCourseLike(id, token);
      setIsLiked(res.is_liked ?? res.liked ?? false);
      setLikesCount(res.likes_count ?? res.likesCount ?? 0);
    } catch (err) {
      console.error('toggle_like error:', err);
    } finally {
      setLikeLoading(false);
    }
  };

  // دالة جلب التعليقات الشجرية (تُستخدم في useEffect أو عند تحديث التعليقات)
  const fetchReviewCommentsHandler = async (reviewId) => {
    try {
      return await fetchReviewComments(reviewId);
    } catch {
      return [];
    }
  };

  // دالة إعجاب/إلغاء إعجاب للتعليق أو الرد
  const handleToggleCommentLike = async (commentId) => {
    try {
      const token = Cookies.get("authToken");
      const res = await toggleCommentLike(commentId, token);
      setReviews((prev) => prev.map((review) => ({
        ...review,
        comments: review.comments?.map((c) =>
          updateCommentLikeRecursive(c, commentId, res)
        ) || [],
      })));
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // دالة إضافة رد
  const handleAddReply = async (parentId, replyText, reviewId) => {
    try {
      const token = Cookies.get("authToken");
      await addReviewReply(reviewId, parentId, replyText, token);
      const comments = await fetchReviewCommentsHandler(reviewId);
      setReviews((prev) => prev.map((review) =>
        review.id === reviewId ? { ...review, comments } : review
      ));
    } catch (err) {
      // يمكن عرض رسالة خطأ
    }
  };

  // مكون عرض التعليقات الشجرية
  function CommentTree({ comments, reviewId }) {
    const [replyingTo, setReplyingTo] = useState(null);
    const [replyText, setReplyText] = useState("");
    return (
      <div>
        {comments?.map((comment) => (
          <div key={comment.id} className="mt-2 p-2 bg-gray-100 rounded ml-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-800">{comment.user?.username || "مستخدم"}: {comment.text}</span>
              <div className="flex items-center gap-2">
                <button
                  className={`text-sm ${comment.is_liked ? 'text-blue-600' : 'text-gray-600'}`}
                  onClick={() => handleToggleCommentLike(comment.id)}
                >
                  👍 {comment.likes_count || 0}
                </button>
                <button
                  className="text-sm text-green-600"
                  onClick={() => setReplyingTo(comment.id)}
                >رد</button>
              </div>
            </div>
            {replyingTo === comment.id && (
              <form
                className="flex gap-2 mt-2"
                onSubmit={e => {
                  e.preventDefault();
                  if (replyText.trim()) {
                    handleAddReply(comment.id, replyText, reviewId);
                    setReplyText("");
                    setReplyingTo(null);
                  }
                }}
              >
                <input
                  className="border rounded p-1 flex-1"
                  value={replyText}
                  onChange={e => setReplyText(e.target.value)}
                  placeholder="اكتب ردك..."
                />
                <button type="submit" className="bg-primary text-white px-2 rounded">إرسال</button>
                <button type="button" className="text-gray-500" onClick={() => setReplyingTo(null)}>إلغاء</button>
              </form>
            )}
            {/* عرض الردود بشكل شجري */}
            {comment.replies && comment.replies.length > 0 && (
              <CommentTree comments={comment.replies} reviewId={reviewId} />
            )}
          </div>
        ))}
      </div>
    );
  }

  // إضافة state للمودال (يجب أن تكون داخل دالة CoursePage)
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState(null); // 'exam' | 'assignment'
  const [modalId, setModalId] = useState(null);
  const [modalDuration, setModalDuration] = useState(0);
  const [modalQuestions, setModalQuestions] = useState([]); // state جديد للأسئلة الممررة للمودال

  // تعديل فتح مودال الامتحان لجلب الوقت المتبقي من الباك اند
  const handleOpenExamModal = async (examIdParam) => {
    console.log('زر حل الامتحان تم الضغط عليه - zaki alkholy');
    try {
      const token = Cookies.get("authToken");
      const usedExamId = examIdParam || examId;
      console.log('examId:', usedExamId);
      console.log('token:', token);
      if (token && usedExamId) {
        // أولاً، تحقق من حالة الامتحان
        const statusRes = await fetchExamStatus(usedExamId, token);
        console.log('zaki alkholy - fetchExamStatus response:', statusRes);

        if (statusRes.status === 'submitted') {
          // الامتحان اتسلم خلاص، اعرض النتيجة
          setModalType('exam');
          setModalId(usedExamId);
          setModalDuration(0); // مافيش وقت لأن الامتحان خلص
          setModalQuestions(statusRes.questions || []);
          setModalOpen(true);
        } else {
          // الامتحان لسه مش اتسلم، جيب الوقت المتبقي - zaki alkholy
          const res = await fetchExamTimeLeft(usedExamId, token);
          console.log('zaki alkholy - fetchExamTimeLeft response:', res);
          console.log('zaki alkholy - res.questions:', res.questions);

          if (res.status === 'auto_submitted') {
            // تم التسليم التلقائي، اعرض النتيجة - zaki alkholy
            alert(res.message);
            // إعادة جلب حالة الامتحان لعرض النتيجة
            const updatedStatus = await fetchExamStatus(usedExamId, token);
            setModalType('exam');
            setModalId(usedExamId);
            setModalDuration(0);
            setModalQuestions(updatedStatus.questions || []);
            setModalOpen(true);
          } else if (res.time_left > 0) {
            setModalType('exam');
            setModalId(usedExamId);
            setModalDuration(res.time_left); // الوقت المتبقي الصحيح - zaki alkholy
            setModalQuestions(res.questions || []);
            setModalOpen(true);
          } else {
            alert("انتهى وقت الامتحان أو لا يمكنك الدخول.");
          }
        }
      }
    } catch (e) {
      alert(e?.response?.data?.error || "لا يمكنك دخول الامتحان أو انتهى الوقت.");
    }
  };

  // إضافة حالة الامتحان examStatus في CoursePage
  const [examStatus, setExamStatus] = useState(null);
  const [examLoading, setExamLoading] = useState(false);
  const [examError, setExamError] = useState(null);

  // جلب حالة الامتحان عند تحميل الصفحة أو تغيير الكورس
  const examId = course?.exam_id;
  const assignmentId = course?.assignment_id;
  useEffect(() => {
    if (!examId) return;
    const fetchStatus = async () => {
      setExamLoading(true);
      setExamError(null);
      try {
        const token = Cookies.get("authToken");
        if (!token) return;
        const status = await fetchExamStatus(examId, token);
        setExamStatus(status);
      } catch (e) {
        setExamError("تعذر جلب حالة الامتحان");
      }
      setExamLoading(false);
    };
    fetchStatus();
  }, [examId]);

  // حالة كل كويز لكل درس
  const [quizStatuses, setQuizStatuses] = useState({});

  // جلب حالة كل كويز عند تحميل الدروس
  useEffect(() => {
    if (!lessons || !user) return;
    const token = Cookies.get("authToken");
    const fetchStatuses = async () => {
      const statuses = {};
      for (const lesson of lessons) {
        if (lesson.quizzes && lesson.quizzes.length > 0) {
          for (const quiz of lesson.quizzes) {
            try {
              const res = await fetchExamStatus(quiz.id, token);
              statuses[quiz.id] = res;
            } catch (e) {
              statuses[quiz.id] = null;
            }
          }
        }
      }
      setQuizStatuses(statuses);
    };
    fetchStatuses();
  }, [lessons, user]);

  // دالة لتحديث حالة الامتحان بعد التسليم
  const handleExamSubmitted = async (quizId) => {
    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      // تحديث حالة الكويز المحدد
      const updatedStatus = await fetchExamStatus(quizId, token);
      setQuizStatuses(prev => ({
        ...prev,
        [quizId]: updatedStatus
      }));

      // إذا كان هذا هو الامتحان الرئيسي للكورس، حدث examStatus أيضاً
      if (quizId === examId) {
        setExamStatus(updatedStatus);
      }
    } catch (e) {
      console.error('خطأ في تحديث حالة الامتحان:', e);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-xl">لم يتم العثور على الكورس</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* صورة الكورس */}
          <div className="relative h-96 w-full bg-gray-200 flex items-center justify-center">
            {course.thumbnail ? (
              <Image
                src={`https://res.cloudinary.com/di5y7hnub/${course.thumbnail}`}
                alt={course.title}
                fill
                className="object-cover"
                priority
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100">
                <span className="text-gray-500 text-xl">
                  لا توجد صورة للكورس
                </span>
              </div>
            )}
          </div>

          {/* معلومات الكورس */}
          <div className="p-6">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {course.title}
                </h1>
                <p className="text-gray-600 text-lg">
                  {course.short_description}
                </p>
              </div>
              <div className="flex flex-col items-end gap-2">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="mr-1 text-gray-700">
                      {course.rating || 0}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-gray-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path d="M18 8a2 2 0 11-4 0 2 2 0 014 0z" />
                      <path d="M14 15a4 4 0 00-8 0v3h8v-3z" />
                      <path d="M6 8a2 2 0 11-4 0 2 2 0 014 0z" />
                      <path d="M16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3z" />
                      <path d="M4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                    </svg>
                    <span className="mr-1 text-gray-700">
                      {course.students_count || 0} طالب
                    </span>
                  </div>
                </div>
                {/* زر الإعجاب وعداد الإعجابات */}
                <button
                  onClick={handleToggleLike}
                  disabled={likeLoading}
                  className={`flex items-center gap-1 px-3 py-1 rounded transition-colors ${isLiked ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'} hover:bg-blue-200 ${likeLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <svg
                    className={`w-5 h-5 ${isLiked ? 'fill-blue-500' : 'fill-gray-400'}`}
                    viewBox="0 0 20 20"
                  >
                    <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 18.657l-6.828-6.829a4 4 0 010-5.656z" />
                  </svg>
                  <span>{isLiked ? 'إلغاء الإعجاب' : 'إعجاب'}</span>
                  <span className="ml-2 text-xs">{likesCount}</span>
                  {likeLoading && (
                    <span className="ml-2 animate-spin w-4 h-4 border-b-2 border-blue-500 rounded-full inline-block"></span>
                  )}
                </button>
              </div>
            </div>

            {/* صورة المدرس */}
            <div className="flex items-center mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="w-16 h-16 mr-4 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                {course.instructor?.profile_image ? (
                  <Image
                    src={getProfileImageUrl(course.instructor.profile_image)}
                    alt={course.instructor?.username || "صورة المدرس"}
                    width={64}
                    height={64}
                    className="rounded-full object-cover"
                    sizes="64px"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <span className="text-gray-500 text-xs">صورة</span>
                  </div>
                )}
              </div>
              <div>
                <p className="text-gray-900 font-medium text-lg">
                  {course.instructor?.username}
                </p>
                <p className="text-gray-600">{course.instructor?.bio}</p>
              </div>
            </div>

            <div className="prose max-w-none mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                وصف الكورس
              </h2>
              <p className="text-gray-600 leading-relaxed">
                {course.description}
              </p>
            </div>

            {/* تفاصيل الكورس */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  المستوى
                </h3>
                <p className="text-gray-600">{course.level}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  اللغة
                </h3>
                <p className="text-gray-600">{course.language}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  السعر
                </h3>
                <p className="text-gray-600">
                  {course.discount_price ? (
                    <>
                      <span className="line-through text-gray-400">
                        {course.price}
                      </span>
                      <span className="text-primary mr-2">
                        {course.discount_price}
                      </span>
                    </>
                  ) : (
                    <span>{course.price}</span>
                  )}
                  {course.currency}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  عدد الدروس
                </h3>
                <p className="text-gray-600">{lessons.length} درس</p>
              </div>
            </div>

            {/* متطلبات الكورس */}
            {course.prerequisites && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  متطلبات الكورس
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.prerequisites}</p>
                </div>
              </div>
            )}

            {/* مخرجات التعلم */}
            {course.learning_outcomes && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  مخرجات التعلم
                </h2>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-600">{course.learning_outcomes}</p>
                </div>
              </div>
            )}

            {/* محتوى الكورس */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                محتوى الكورس
              </h2>
              <div className="space-y-4">
                {Array.isArray(lessons) && lessons.length > 0 ? (
                  lessons.map((lesson, index) => (
                    <div
                      key={lesson.id}
                      className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="text-gray-500 ml-3">{index + 1}</span>
                          <h3 className="text-lg font-medium text-gray-900">
                            {lesson.title}
                          </h3>
                          {lesson.is_preview && (
                            <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded mr-2">
                              معاينة
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 rtl:space-x-reverse">
                          <span className="text-gray-500">
                            {lesson.duration} دقيقة
                          </span>
                          <span className="text-gray-500">
                            {lesson.lesson_type}
                          </span>
                          {(lesson.is_preview || isEnrolled) && (
                            <button
                              onClick={() => handleVideo(lesson)}
                              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary bg-primary/10 rounded-lg hover:bg-primary/20 transition-colors"
                            >
                              <svg
                                className="w-4 h-4 ml-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                />
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                />
                              </svg>
                              {lesson.is_preview ? "معاينة" : "مشاهدة"}
                            </button>
                          )}
                        </div>
                      </div>

                      {/* روابط الامتحان/الواجب/الملف لكل درس */}
                      <div className="flex flex-wrap gap-3 mt-3">
                        {/* أزرار الامتحان/الواجب */}
                        {lesson.quizzes && lesson.quizzes.length > 0 && lesson.quizzes.map((quiz) => {
                          // منطق إظهار زر "إظهار النتيجة" إذا الطالب سلّم الامتحان
                          const quizStatus = quizStatuses[quiz.id];
                          const isSubmitted = quizStatus?.status === 'submitted';
                          return (
                            isSubmitted ? (
                              <button
                                key={quiz.id}
                                onClick={() => handleOpenExamModal(quiz.id)}
                                className="bg-green-600 text-white px-3 py-1 rounded"
                              >
                                إظهار النتيجة
                              </button>
                            ) : (
                              <button
                                key={quiz.id}
                                onClick={() => handleOpenExamModal(quiz.id)}
                                className={
                                  quiz.quiz_type === 'exam'
                                    ? 'bg-blue-600 text-white px-3 py-1 rounded'
                                    : 'bg-green-600 text-white px-3 py-1 rounded'
                                }
                              >
                                {quiz.quiz_type === 'exam' ? 'حل الامتحان' : 'حل الواجب'}
                              </button>
                            )
                          );
                        })}
                        {/* زر تحميل ملف الدرس */}
                        {lesson.resources && (
                          <a
                            href={lesson.resources}
                            target="_blank"
                            rel="noopener noreferrer"
                            download
                            className="bg-gray-700 text-white px-3 py-1 rounded"
                          >
                            تحميل ملف الدرس
                          </a>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center">
                    لا توجد دروس متاحة حالياً
                  </p>
                )}
              </div>
            </div>

            {/* التقييمات والتعليقات */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">تقييمات الطلاب</h2>
              {reviewLoading ? (
                <div>جاري التحميل...</div>
              ) : reviews.length === 0 ? (
                <div className="text-gray-500">لا توجد تقييمات بعد</div>
              ) : (
                <div className="space-y-4">
                  {reviews.map((review) => (
                    <div key={review.id} className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center mb-2">
                        <span className="font-bold text-gray-800 mr-2">{review.user?.username || "مستخدم"}</span>
                        <span className="text-yellow-500 ml-2">{'★'.repeat(review.rating)}{'☆'.repeat(5 - review.rating)}</span>
                      </div>
                      <div className="text-gray-700">{review.comment}</div>
                      <div className="text-xs text-gray-400 mt-1">{new Date(review.created_at).toLocaleDateString()}</div>
                      {review.reply && (
                        <div className="mt-2 p-2 bg-blue-50 rounded text-blue-800">
                          <span><span className="font-bold">رد المدرب:</span> {review.reply}</span>
                        </div>
                      )}
                      {/* عرض التعليقات الشجرية */}
                      <CommentTree comments={review.comments} reviewId={review.id} />
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* فورم إضافة تقييم */}
            {user && isEnrolled && (
              <div className="mb-8">
                <h3 className="text-lg font-bold mb-2">أضف تقييمك للكورس</h3>
                <form onSubmit={handleSubmit(onSubmitReview)} className="space-y-2">
                  <div>
                    <label className="block mb-1">عدد النجوم:</label>
                    <select {...register("rating", { required: true })} className="border rounded p-2">
                      <option value="">اختر</option>
                      {[1,2,3,4,5].map(n => <option key={n} value={n}>{n}</option>)}
                    </select>
                  </div>
                  <div>
                    <label className="block mb-1">تعليقك:</label>
                    <textarea {...register("comment", { required: true })} className="border rounded p-2 w-full" rows={3} />
                  </div>
                  <button type="submit" className="bg-primary text-white px-4 py-2 rounded">إرسال</button>
                  {reviewError && <div className="text-red-500 mt-2">{reviewError}</div>}
                  {reviewSuccess && <div className="text-green-600 mt-2">{reviewSuccess}</div>}
                </form>
              </div>
            )}

            {/* زر التسجيل */}
            {user ? (
              isEnrolled ? (
                <div className="text-center">
                  <p className="text-green-600 mb-4">أنت مسجل في هذا الكورس</p>
                  <Link
                    href={`/student/course/${id}/learn`}
                    className="inline-block bg-primary text-white py-4 px-6 rounded-lg font-medium text-lg hover:bg-primary/90 transition-colors"
                  >
                    متابعة التعلم
                  </Link>
                </div>
              ) : (
                <>
                  {id ? (
                    <PaymentForm
                      courseId={id}
                      coursePrice={course?.discount_price || course?.price}
                    />
                  ) : (
                    <div className="text-red-500 text-center">
                      خطأ: معرّف الكورس غير متوفر
                    </div>
                  )}
                </>
              )
            ) : (
              <div className="text-center">
                <p className="text-gray-600 mb-4">
                  يجب تسجيل الدخول للتسجيل في الكورس
                </p>
                <Link
                  href="/login"
                  className="inline-block bg-primary text-white py-4 px-6 rounded-lg font-medium text-lg hover:bg-primary/90 transition-colors"
                >
                  تسجيل الدخول
                </Link>
              </div>
            )}

            {/* أزرار حل الامتحان/الواجب */}
            <div className="flex gap-4 mb-8">
              {examId && (
                examStatus?.submitted || examStatus?.time_left === 0 ? (
                  <button
                    type="button"
                    onClick={() => handleOpenExamModal(examId)}
                    className="bg-green-600 text-white px-4 py-2 rounded"
                  >
                    عرض النتيجة - {course?.exam_name}
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={() => handleOpenExamModal(examId)}
                    className="bg-blue-600 text-white px-4 py-2 rounded"
                  >
                    حل الامتحان - {course?.exam_name}
                  </button>
                )
              )}
              {assignmentId && (
                <button
                  type="button"
                  onClick={() => {
                    setModalType("assignment");
                    setModalId(assignmentId);
                    setModalDuration(0);
                    setModalOpen(true);
                  }}
                  className="bg-green-600 text-white px-4 py-2 rounded"
                >
                  حل الواجب
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* مودال الامتحان/الواجب */}
      <ExamAssignmentModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        type={modalType}
        id={modalId}
        duration={modalType === "exam" ? modalDuration : undefined}
        questions={modalQuestions}
        examStatus={modalId === examId ? examStatus : quizStatuses[modalId]} // تمرير حالة الامتحان الصحيحة
        onExamSubmitted={handleExamSubmitted} // تمرير دالة callback
      />

      {previewVideoUrl && isClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold text-gray-900">
                {selectedLesson?.title} -{" "}
                {selectedLesson?.is_preview ? "معاينة" : "مشاهدة"}
              </h3>
              <button
                onClick={() => {
                  setPreviewVideoUrl(null);
                  setSelectedLesson(null);
                  if (player) {
                    player.destroy();
                    setPlayer(null);
                  }
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="p-4">
              {previewLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                </div>
              ) : previewError ? (
                <div className="text-red-500 text-center p-4">
                  {previewError}
                </div>
              ) : (
                <div className="plyr__video-embed">
                  <video
                    ref={videoRef}
                    src={previewVideoUrl}
                    className="w-full rounded-lg"
                    style={{ maxHeight: "70vh" }}
                    playsInline
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
